#!/usr/bin/env elixir

# Debug script to understand Sourceror.Zipper behavior

Mix.install([
  {:sourceror, "~> 1.0"}
])

alias Sourceror.Zipper

# Create a simple AST
original_ast = quote do
  defmodule TestSchema do
    use Ecto.Schema
    import Ecto.Schema

    schema "users" do
      field(:name, :string)
      field(:email, :string)
      timestamps()
    end

    def custom_function(arg) do
      arg
    end
  end
end

IO.puts("=== Original AST ===")
IO.inspect(original_ast, label: "AST")

# Create a zipper
zipper = Zipper.zip(original_ast)
IO.puts("\n=== Zipper ===")
IO.inspect(zipper, label: "Zipper")

# Test navigation
IO.puts("\n=== Navigation Test ===")
case Zipper.down(zipper) do
  nil ->
    IO.puts("Cannot navigate down")
  
  body_zipper ->
    IO.puts("Successfully navigated down")
    IO.inspect(Zipper.node(body_zipper), label: "Body node")
    
    # Test insert_right
    new_attr = quote do: @primary_key {:id, Ecto.UUID, autogenerate: true}
    
    updated_zipper = Zipper.insert_right(body_zipper, new_attr)
    IO.inspect(updated_zipper, label: "After insert_right")
    
    # Test up
    back_up = Zipper.up(updated_zipper)
    IO.inspect(back_up, label: "After up")
    
    # Test top
    top_zipper = Zipper.top(updated_zipper)
    IO.inspect(top_zipper, label: "After top")
    
    # Check if top_zipper is still a zipper
    IO.puts("Is top_zipper a Zipper struct? #{is_struct(top_zipper, Zipper)}")

    if is_struct(top_zipper, Zipper) do
      IO.puts("Top zipper node:")
      IO.inspect(Zipper.node(top_zipper), label: "Top node")
    else
      IO.puts("Top zipper is not a Zipper struct!")
      IO.inspect(top_zipper, label: "Top result")
    end
end
