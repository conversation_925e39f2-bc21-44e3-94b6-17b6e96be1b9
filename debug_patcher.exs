#!/usr/bin/env elixir

# Debug script to test the patcher step by step

alias Drops.Relation.Schema
alias Drops.Relation.Schema.{Field, PrimaryKey, Patcher}
alias Drops.Relation.Compilers.CodeCompiler

# Create a simple schema like in the test
name_field = Field.new(:name, :string, %{source: :name})
age_field = Field.new(:age, :integer, %{source: :age})
id_field = Field.new(:id, Ecto.UUID, %{source: :id, primary_key: true})

primary_key = PrimaryKey.new([id_field])
schema = Schema.new("users", primary_key, [], [name_field, age_field, id_field], [])

# Compile the schema to get the parts
compiled_parts = CodeCompiler.visit(schema, grouped: true)
IO.inspect(compiled_parts, label: "compiled_parts")

# Create original schema module AST
original_ast = quote do
  defmodule TestSchema do
    use Ecto.Schema
    import Ecto.Schema

    schema "users" do
      field(:name, :string)
      field(:email, :string)
      timestamps()
    end

    def custom_function(arg) do
      arg
    end
  end
end

# Create a zipper from the original AST
zipper = Sourceror.Zipper.zip(original_ast)

# Test each step individually
IO.puts("\n=== Testing update_attributes ===")
try do
  updated_zipper = Patcher.update_attributes(zipper, compiled_parts.attributes)
  IO.puts("✓ update_attributes succeeded")
  
  IO.puts("\n=== Testing update_schema_block ===")
  final_zipper = Patcher.update_schema_block(updated_zipper, compiled_parts.field_definitions, "users")
  IO.puts("✓ update_schema_block succeeded")
  
  # Convert to string for inspection
  final_ast = Sourceror.Zipper.root(final_zipper)
  final_code = Macro.to_string(final_ast)
  IO.puts("\n=== Final result ===")
  IO.puts(final_code)
  
rescue
  error ->
    IO.puts("✗ Failed")
    IO.inspect(error, label: "error")
    IO.inspect(__STACKTRACE__, label: "stacktrace")
end
