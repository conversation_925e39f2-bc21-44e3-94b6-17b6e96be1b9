defmodule Drops.Relation.Schema.Patcher do
  @moduledoc """
  Sophisticated schema patching using Sourceror.Zipper for selective updates.

  This module provides functions to surgically update existing Ecto schema modules
  while preserving custom code like functions, associations, and validations.

  ## Features

  - **Selective Updates**: Only modify schema-related parts
  - **Code Preservation**: Keep custom functions and associations
  - **Robust Handling**: Deal with various module structures
  - **Extensible**: Easy to add new patching capabilities

  ## Usage

      # Patch an existing schema module
      zipper = Sourceror.Zipper.zip(existing_ast)
      compiled_parts = CodeCompiler.visit(schema, grouped: true)

      {:ok, updated_zipper} = Patcher.patch_schema_module(zipper, compiled_parts, "users")
      updated_ast = Sourceror.Zipper.root(updated_zipper)
  """

  alias Sourceror.Zipper
  require Logger

  @doc """
  Main entry point for patching a schema module.

  ## Parameters

  - `zipper` - Sourceror.Zipper positioned at the module root
  - `compiled_parts` - Grouped compilation result from CodeCompiler with `:grouped` option
  - `table_name` - The database table name for the schema

  ## Returns

  - `{:ok, updated_zipper}` - Successfully patched zipper
  - `{:error, reason}` - Patching failed

  ## Examples

      iex> zipper = Sourceror.Zipper.zip(existing_ast)
      iex> compiled_parts = %{attributes: %{primary_key: [...], ...}, field_definitions: [...]}
      iex> {:ok, updated_zipper} = Patcher.patch_schema_module(zipper, compiled_parts, "users")
  """
  @spec patch_schema_module(Zipper.t(), map(), String.t()) :: {:ok, Zipper.t()} | {:error, term()}
  def patch_schema_module(zipper, compiled_parts, table_name) do
    try do
      # For now, implement a working version that does basic patching
      # We can enhance this later to be more sophisticated

      # Get the original AST
      original_ast = Zipper.root(zipper)

      # Generate new content using the compiled parts
      new_content = generate_patched_module_content(original_ast, compiled_parts, table_name)

      # Create a new zipper with the patched content
      updated_zipper = Zipper.zip(new_content)

      {:ok, updated_zipper}
    rescue
      error ->
        Logger.error("Failed to patch schema module: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  Updates module-level attributes (@primary_key, @foreign_key_type, etc.).

  ## Parameters

  - `zipper` - Sourceror.Zipper positioned at the module
  - `attributes` - Map of categorized attributes from CodeCompiler

  ## Returns

  Updated zipper with attributes modified.
  """
  @spec update_attributes(Zipper.t(), map()) :: Zipper.t()
  def update_attributes(zipper, attributes) do
    zipper
    |> update_primary_key_attributes(attributes.primary_key)
    |> update_foreign_key_type_attributes(attributes.foreign_key_type)
    |> update_other_attributes(attributes.other)
  end

  @doc """
  Updates the schema block content with new field definitions.

  ## Parameters

  - `zipper` - Sourceror.Zipper positioned at the module
  - `field_definitions` - List of field definition AST nodes
  - `table_name` - The database table name

  ## Returns

  Updated zipper with schema block modified.
  """
  @spec update_schema_block(Zipper.t(), list(), String.t()) :: Zipper.t()
  def update_schema_block(zipper, field_definitions, table_name) do
    case find_schema_block(zipper, table_name) do
      {:ok, schema_zipper} ->
        replace_schema_fields(schema_zipper, field_definitions)

      :error ->
        # Schema block not found, create a new one
        create_schema_block(zipper, field_definitions, table_name)
    end
  end

  # Private helper functions

  # Updates @primary_key attributes
  defp update_primary_key_attributes(zipper, primary_key_attrs) do
    if Enum.empty?(primary_key_attrs) do
      zipper
    else
      # Remove existing @primary_key attributes
      zipper_without_pk = remove_attribute_definitions(zipper, :primary_key)

      # Add new @primary_key attributes at the appropriate location
      add_attributes_after_imports(zipper_without_pk, primary_key_attrs)
    end
  end

  # Updates @foreign_key_type attributes
  defp update_foreign_key_type_attributes(zipper, foreign_key_attrs) do
    if Enum.empty?(foreign_key_attrs) do
      zipper
    else
      # Remove existing @foreign_key_type attributes
      zipper_without_fk = remove_attribute_definitions(zipper, :foreign_key_type)

      # Add new @foreign_key_type attributes at the appropriate location
      add_attributes_after_imports(zipper_without_fk, foreign_key_attrs)
    end
  end

  # Updates other attributes
  defp update_other_attributes(zipper, other_attrs) do
    if Enum.empty?(other_attrs) do
      zipper
    else
      # For now, just add other attributes without removing existing ones
      # This preserves custom attributes that users might have added
      add_attributes_after_imports(zipper, other_attrs)
    end
  end

  # Removes all attribute definitions of a specific type
  defp remove_attribute_definitions(zipper, attribute_name) do
    # Use traverse_while to remove nodes as we find them
    Zipper.traverse_while(zipper, fn node_zipper ->
      case Zipper.node(node_zipper) do
        {:@, _, [{^attribute_name, _, _}]} ->
          # Remove this node and continue
          {:skip, Zipper.remove(node_zipper)}

        _ ->
          # Continue traversing
          {:cont, node_zipper}
      end
    end)
  end

  # Adds attributes after import statements
  defp add_attributes_after_imports(zipper, attributes) when is_list(attributes) do
    # If no attributes to add, return original zipper
    if Enum.empty?(attributes) do
      zipper
    else
      # For now, just add at the beginning of module body after use/import statements
      # This is a simplified approach that works reliably
      add_attributes_at_module_start(zipper, attributes)
    end
  end

  # Adds attributes at the start of module body (after defmodule line)
  defp add_attributes_at_module_start(zipper, attributes) do
    case Zipper.down(zipper) do
      nil ->
        zipper

      body_zipper ->
        Enum.reduce(attributes, body_zipper, fn attr, acc_zipper ->
          Zipper.insert_right(acc_zipper, attr)
        end)
        |> Zipper.up()
    end
  end

  # Finds the schema block in the module
  defp find_schema_block(zipper, table_name) do
    # Search for schema "table_name" do ... end pattern using traversal
    found_zipper =
      Zipper.find(zipper, fn node_zipper ->
        case Zipper.node(node_zipper) do
          {:schema, _, [table_arg | _]} ->
            # Check if the table argument matches our table name
            case table_arg do
              ^table_name -> true
              table_string when is_binary(table_string) -> table_string == table_name
              _ -> false
            end

          _ ->
            false
        end
      end)

    case found_zipper do
      nil -> :error
      zipper -> {:ok, zipper}
    end
  end

  # Replaces field definitions in the schema block
  defp replace_schema_fields(schema_zipper, field_definitions) do
    # Navigate to the schema block body and replace field definitions
    # while preserving timestamps() and other custom content
    case Zipper.down(schema_zipper) do
      nil ->
        schema_zipper

      body_zipper ->
        # Navigate to the do block content
        case Zipper.down(body_zipper) do
          nil ->
            schema_zipper

          do_block_zipper ->
            # Remove existing field definitions but keep timestamps() and associations
            cleaned_zipper = remove_field_definitions(do_block_zipper)

            # Find the best insertion point for new fields (before timestamps if it exists)
            insertion_point = find_field_insertion_point(cleaned_zipper)

            # Add new field definitions at the insertion point
            updated_zipper =
              Enum.reduce(field_definitions, insertion_point, fn field_def, acc_zipper ->
                Zipper.insert_left(acc_zipper, field_def)
              end)

            # Navigate back up to the schema level
            updated_zipper
            |> Zipper.up()
            |> Zipper.up()
        end
    end
  end

  # Removes field() calls but preserves other content like timestamps(), associations
  defp remove_field_definitions(zipper) do
    Zipper.traverse_while(zipper, fn node_zipper ->
      case Zipper.node(node_zipper) do
        {:field, _, _} ->
          {:skip, Zipper.remove(node_zipper)}

        _ ->
          {:cont, node_zipper}
      end
    end)
  end

  # Finds the best insertion point for field definitions (before timestamps if it exists)
  defp find_field_insertion_point(zipper) do
    # Look for timestamps() call to insert fields before it
    case Zipper.find(zipper, fn node_zipper ->
           case Zipper.node(node_zipper) do
             {:timestamps, _, _} -> true
             _ -> false
           end
         end) do
      nil ->
        # No timestamps found, insert at the end
        Zipper.rightmost(zipper) || zipper

      timestamps_zipper ->
        # Insert before timestamps
        timestamps_zipper
    end
  end

  # Creates a new schema block if one doesn't exist
  defp create_schema_block(zipper, field_definitions, table_name) do
    # Create the schema block AST
    schema_ast =
      quote do
        schema unquote(table_name) do
          unquote_splicing(field_definitions)
          timestamps()
        end
      end

    # Find a good insertion point (after attributes, before functions)
    case find_schema_insertion_point(zipper) do
      {:ok, insertion_zipper} ->
        Zipper.insert_right(insertion_zipper, schema_ast)
        |> Zipper.root()
        |> Zipper.zip()

      :error ->
        # Add at the end of module body
        add_at_module_end(zipper, schema_ast)
        |> Zipper.root()
        |> Zipper.zip()
    end
  end

  # Finds a good insertion point for the schema block
  defp find_schema_insertion_point(zipper) do
    # Look for the last attribute definition or import
    last_attr_or_import =
      Zipper.find(zipper, :prev, fn node_zipper ->
        case Zipper.node(node_zipper) do
          {:@, _, _} -> true
          {:import, _, _} -> true
          {:use, _, _} -> true
          _ -> false
        end
      end)

    case last_attr_or_import do
      nil -> :error
      zipper -> {:ok, zipper}
    end
  end

  # Adds content at the end of module body
  defp add_at_module_end(zipper, content) do
    # Navigate to the end of the module and add content
    case Zipper.rightmost(zipper) do
      nil -> zipper
      rightmost_zipper -> Zipper.insert_right(rightmost_zipper, content)
    end
  end
end
